import React, { useEffect, useState } from "react";
import {
  ArrowLeft,
  Heart,
  Share2,
  Bookmark,
  MessageCircle,
  Play,
  RotateCcw,
  RotateCw,
  ArrowUpRight,
} from "lucide-react";
import { Link, useLocation, useParams, useNavigate } from "react-router-dom";
import {
  createSlug,
  findBlogBySlug,
  findBlogById,
} from "../../../utils/slugUtils";

// Import images from public folder using direct paths
const Instagram = "/assets/Blog/Instagram.svg";
const Facebook = "/assets/Blog/Facebook.svg";
const YouTube = "/assets/Blog/YouTube.svg";
const XIcon = "/assets/Blog/Twitter.svg";
const Medium = "/assets/Blog/Medium.svg";
const LinkedIn = "/assets/Blog/Linkedin.svg";

// Skeleton Loader Component
const BlogSkeleton = () => {
  return (
    <div className="min-h-screen bg-white">
      <div className="py-10 mt-[80px]">
        {/* Hero Image Skeleton */}
        <div className="h-[476px] w-[1683px] mx-auto bg-gray-200 animate-pulse mb-8 mt-[20px] rounded-3xl max-w-[90%]" />

        {/* Title Skeleton */}
        <div className="h-12 bg-gray-200 rounded-lg animate-pulse w-3/4 mx-auto mb-6 mt-6" />

        {/* Meta Badges Skeleton */}
        <div className="meta-info mb-8 flex justify-center gap-2">
          {[...Array(3)].map((_, i) => (
            <span
              key={i}
              className="bg-gray-300 animate-pulse rounded px-3 py-2"
              style={{ minWidth: 70, minHeight: 28 }}
            />
          ))}
        </div>

        {/* Subtitle Skeleton */}
        <div className="h-6 bg-gray-200 rounded mb-8 w-2/3 mx-auto animate-pulse" />

        {/* Content Paragraph Skeletons */}
        <div className="max-w-4xl mx-auto px-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="mb-6">
              <div className="h-5 bg-gray-200 rounded animate-pulse w-full mb-2" />
              <div className="h-5 bg-gray-200 rounded animate-pulse w-5/6 mb-2" />
              <div className="h-5 bg-gray-200 rounded animate-pulse w-4/5" />
            </div>
          ))}

          {/* Section Heading Skeleton */}
          <div className="h-8 bg-gray-200 rounded-lg animate-pulse w-2/3 mb-4" />

          {/* List Skeleton */}
          <div className="mb-8 pl-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="mb-3">
                <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3" />
              </div>
            ))}
          </div>

          {/* Another Section Heading Skeleton */}
          <div className="h-8 bg-gray-200 rounded-lg animate-pulse w-2/3 mb-4" />

          {/* More Content Paragraph Skeletons */}
          {[...Array(4)].map((_, i) => (
            <div key={i} className="mb-6">
              <div className="h-5 bg-gray-200 rounded animate-pulse w-full mb-2" />
              <div className="h-5 bg-gray-200 rounded animate-pulse w-5/6 mb-2" />
              <div className="h-5 bg-gray-200 rounded animate-pulse w-4/5" />
            </div>
          ))}
        </div>

        {/* Social Icons Skeleton */}
        <div className="flex justify-center items-center gap-3 pt-8 mb-8">
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"
            />
          ))}
        </div>

        {/* More Insights Section Skeleton */}
        <div className="bg-gray-100 rounded-tl-[40px] rounded-bl-[40px] p-8 mt-[60px] md:ml-[5vw] mr-0 mb-[60px]">
          <div className="h-8 bg-gray-200 rounded animate-pulse w-64 mb-8" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-4">
                <div className="h-48 bg-gray-200 rounded-xl animate-pulse" />
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
                <div className="h-6 bg-gray-200 rounded animate-pulse w-full" />
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-full" />
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-2/3" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper to get n random items from an array
function getRandomItems(arr, n) {
  const shuffled = arr.slice().sort(() => 0.5 - Math.random());
  return shuffled.slice(0, n);
}

function SingleBlogView() {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [blogData, setBlogData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [localBlogs, setLocalBlogs] = useState([]);

  // Force scroll to top function
  const scrollToTop = () => {
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
    window.scrollTo(0, 0);
  };

  const handleBlogClick = (blog) => {
    if (blog.id) {
      const blogSlug = createSlug(blog.title);
      // Force scroll to top immediately
      scrollToTop();
      navigate(`/blog/${blogSlug}`);
    }
  };

  useEffect(() => {
    const fetchBlog = async () => {
      setLoading(true);
      setError(null);
      try {
        // Directly fetch the blog using the slug as the 'id' param
        const response = await fetch(
          `https://flexioninfotech.com/api/get-blog-by-id/?id=${slug}`
        );
        if (!response.ok) throw new Error("Network response was not ok");
        const data = await response.json();
        setBlogData(data.data);
      } catch (err) {
        setError("Failed to fetch blog.");
      } finally {
        setLoading(false);
      }
    };
    fetchBlog();

    // Fetch blogs from local storage for "More Insights"
    const storedBlogs = localStorage.getItem("flowkar_explore_blogs");
    if (storedBlogs) {
      try {
        const blogsArray = JSON.parse(storedBlogs);
        setLocalBlogs(
          Array.isArray(blogsArray) ? getRandomItems(blogsArray, 3) : []
        );
      } catch (e) {
        setLocalBlogs([]);
      }
    } else {
      setLocalBlogs([]);
    }
  }, [slug, navigate]);

  // Scroll to top when slug changes
  useEffect(() => {
    scrollToTop();
    // Additional scroll with delay to override any scroll restoration
    setTimeout(() => {
      scrollToTop();
    }, 0);
    setTimeout(() => {
      scrollToTop();
    }, 100);
  }, [slug]);

  // Scroll to top after content is fully loaded and rendered
  useEffect(() => {
    if (!loading && blogData) {
      // Multiple attempts to ensure scroll works
      scrollToTop();
      setTimeout(() => {
        scrollToTop();
      }, 0);
      setTimeout(() => {
        scrollToTop();
      }, 100);
      setTimeout(() => {
        scrollToTop();
      }, 300);
    }
  }, [loading, blogData]);

  // Show skeleton loader immediately while loading
  if (loading) {
    return <BlogSkeleton />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center text-red-500 bg-white">
        {error}
      </div>
    );
  }

  if (!blogData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        No blog found.
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="w-[90%] mx-auto px-4 py-6 mt-[80px] overflow-x-hidden">
        {/* Blog Content (HTML) */}
        <div
          className="prose max-w-none"
          dangerouslySetInnerHTML={{ __html: blogData.content }}
        />

        <div className="flex flex-col md:flex-row justify-center items-center">
          {/* Social icons */}
          <div className="flex items-center gap-3 pt-8 md:pt-4">
            <a
              href="https://www.instagram.com/flexion_infotech/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 cursor-pointer transition-colors"
            >
              <span className="text-xs font-semibold text-gray-600">
                <img src={Instagram} alt="Instagram" />
              </span>
            </a>
            <a
              href="https://www.youtube.com/results?search_query=flexion+infotech"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 cursor-pointer transition-colors"
            >
              <span className="text-xs font-semibold text-gray-600">
                <img src={YouTube} alt="YouTube" />
              </span>
            </a>
            <a
              href="https://www.facebook.com/flexion.infotech/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 cursor-pointer transition-colors"
            >
              <span className="text-xs font-semibold text-gray-600">
                <img src={Facebook} alt="Facebook" />
              </span>
            </a>
            <a
              href="https://medium.com/@flexion_infotech"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 cursor-pointer transition-colors"
            >
              <span className="text-xs font-semibold text-gray-600">
                <img src={Medium} alt="X (Twitter)" />
              </span>
            </a>
            <a
              href="https://www.linkedin.com/company/flexion-infotech-india/?viewAsMember=true"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 cursor-pointer transition-colors"
            >
              <span className="text-xs font-semibold text-gray-600">
                <img src={LinkedIn} alt="X (Twitter)" />
              </span>
            </a>
            <a
              href="https://x.com/flexion_info"
              target="_blank"
              rel="noopener noreferrer"
              className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 cursor-pointer transition-colors"
            >
              <span className="text-xs font-semibold text-gray-600">
                <img src={XIcon} alt="X (Twitter)" />
              </span>
            </a>
          </div>
        </div>
      </div>
      {/*  More Insights */}
      <div
        className="bg-[#f9f8f2] md:rounded-tl-[40px] md:rounded-bl-[40px] p-8 text-black mt-[60px] md:ml-[5vw] mr-0 mb-[60px] overflow-hidden text-center md:text-left"
        style={{ borderTopRightRadius: 0, borderBottomRightRadius: 0 }}
      >
        <h2 className="text-2xl font-semibold mb-8">Discover More Insights</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {localBlogs.length > 0 ? (
            localBlogs.map((insight, idx) => (
              <div
                key={insight.id || idx}
                className="group cursor-pointer"
                onClick={() => handleBlogClick(insight)}
              >
                <div className="relative mb-4 overflow-hidden rounded-xl">
                  <img
                    src={
                      insight.image ||
                      "https://via.placeholder.com/500x200?text=No+Image"
                    }
                    alt={insight.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute top-3 right-3">
                    <div className="w-8 h-8 bg-white/20 backdrop-blur rounded-full flex items-center justify-center text-[#FF7731] opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <ArrowUpRight className="w-4 h-4" />
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="text-sm text-black">
                    {(insight.author || "Unknown Author") +
                      " • " +
                      (insight.date || "")}
                  </div>
                  <h3 className="text-lg font-semibold transition-colors">
                    {insight.title}
                  </h3>
                  <p className="text-sm text-black leading-relaxed">
                    {insight.subtitle || ""}
                  </p>
                  <div className="flex justify-center md:justify-start flex-wrap gap-2">
                    {(insight.tags || []).map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 rounded text-xs font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-3 text-center text-black">
              No local blogs found.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default SingleBlogView;
